<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Native Integration | DrawnUI for .NET MAUI </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Native Integration | DrawnUI for .NET MAUI ">
      
      <meta name="description" content="DrawnUI for .NET MAUI - Rendering engine built on SkiaSharp. Create pixel-perfect cross-platform apps for iOS, Android, Windows, MacCatalyst with advanced animations, gestures, and visual effects.">
      <link rel="icon" href="../../images/draw.svg">
      <link rel="stylesheet" href="../../public/docfx.min.css">
      <link rel="stylesheet" href="../../public/main.css">
      <meta name="docfx:navrel" content="../../toc.html">
      <meta name="docfx:tocrel" content="../toc.html">
      
      <meta name="docfx:rel" content="../../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/drawnui/blob/main/docs/articles/controls/native-integration.md/#L1">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../../index.html">
            <img id="logo" class="svg" src="../../images/draw.svg" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="">
<h1 id="native-integration">Native Integration</h1>

<p>DrawnUi.Maui provides seamless integration with native MAUI controls through the <code>SkiaMauiElement</code> control. This allows you to embed standard MAUI controls like WebView, MediaElement, and others within your DrawnUI canvas while maintaining hardware acceleration and performance.</p>
<h2 id="skiamauielement">SkiaMauiElement</h2>
<p><code>SkiaMauiElement</code> is a wrapper control that enables embedding native MAUI <code>VisualElement</code> controls within the DrawnUI rendering pipeline. This is essential for controls that require native platform implementations or when you need to integrate existing MAUI controls into your DrawnUI application.</p>
<h3 id="key-features">Key Features</h3>
<ul>
<li><strong>Native Control Embedding</strong>: Wrap any MAUI VisualElement within DrawnUI</li>
<li><strong>Platform Optimization</strong>: Automatic platform-specific handling (snapshots on Windows, direct rendering on other platforms)</li>
<li><strong>Gesture Coordination</strong>: Proper gesture handling between DrawnUI and native controls</li>
<li><strong>Binding Support</strong>: Full data binding support for embedded controls</li>
<li><strong>Performance</strong>: Optimized rendering with minimal overhead</li>
</ul>
<h3 id="basic-usage">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaMauiElement
    HorizontalOptions=&quot;Fill&quot;
    VerticalOptions=&quot;Fill&quot;&gt;
    
    &lt;!-- Any MAUI VisualElement can be embedded --&gt;
    &lt;Entry
        Placeholder=&quot;Enter text here&quot;
        Text=&quot;{Binding UserInput}&quot; /&gt;
        
&lt;/draw:SkiaMauiElement&gt;
</code></pre>
<h3 id="webview-integration">WebView Integration</h3>
<p>One of the most common use cases is embedding a WebView for displaying web content within your DrawnUI application:</p>
<pre><code class="lang-xml">&lt;draw:SkiaLayout
    HorizontalOptions=&quot;Fill&quot;
    VerticalOptions=&quot;Fill&quot;
    Type=&quot;Column&quot;&gt;

    &lt;!-- Header --&gt;
    &lt;draw:SkiaLayout
        BackgroundColor=&quot;{StaticResource Gray600}&quot;
        HorizontalOptions=&quot;Fill&quot;
        HeightRequest=&quot;60&quot;
        Type=&quot;Row&quot;
        Spacing=&quot;16&quot;
        Padding=&quot;16,0&quot;&gt;

        &lt;draw:SkiaButton
            Text=&quot;← Back&quot;
            TextColor=&quot;White&quot;
            BackgroundColor=&quot;Transparent&quot;
            VerticalOptions=&quot;Center&quot; /&gt;

        &lt;draw:SkiaLabel
            x:Name=&quot;LabelTitle&quot;
            Text=&quot;Web Browser&quot;
            TextColor=&quot;White&quot;
            FontSize=&quot;18&quot;
            VerticalOptions=&quot;Center&quot;
            HorizontalOptions=&quot;Start&quot; /&gt;

    &lt;/draw:SkiaLayout&gt;

    &lt;!-- Background --&gt;
    &lt;draw:SkiaControl
        BackgroundColor=&quot;{StaticResource Gray600}&quot;
        HorizontalOptions=&quot;Fill&quot;
        VerticalOptions=&quot;Fill&quot;
        ZIndex=&quot;-1&quot; /&gt;

    &lt;!-- WebView Content --&gt;
    &lt;draw:SkiaMauiElement
        Margin=&quot;1,0&quot;
        HorizontalOptions=&quot;Fill&quot;
        VerticalOptions=&quot;Fill&quot;&gt;

        &lt;WebView
            x:Name=&quot;ControlBrowser&quot;
            HorizontalOptions=&quot;FillAndExpand&quot;
            VerticalOptions=&quot;FillAndExpand&quot; /&gt;

    &lt;/draw:SkiaMauiElement&gt;

&lt;/draw:SkiaLayout&gt;
</code></pre>
<h3 id="code-behind-implementation">Code-Behind Implementation</h3>
<pre><code class="lang-csharp">public partial class ScreenBrowser
{
    public ScreenBrowser(string title, string source, bool isUrl = true)
    {
        InitializeComponent();

        LabelTitle.Text = title;

        if (isUrl)
        {
            if (string.IsNullOrEmpty(source))
            {
                source = &quot;about:blank&quot;;
            }
            var url = new UrlWebViewSource
            {
                Url = source
            };
            ControlBrowser.Source = url;
        }
        else
        {
            if (string.IsNullOrEmpty(source))
            {
                source = &quot;&quot;;
            }
            var html = new HtmlWebViewSource
            {
                Html = source
            };
            ControlBrowser.Source = html;
        }
    }
}
</code></pre>
<h3 id="platform-specific-behavior">Platform-Specific Behavior</h3>
<p><code>SkiaMauiElement</code> handles platform differences automatically:</p>
<p><strong>Windows:</strong></p>
<ul>
<li>Uses bitmap snapshots for rendering native controls within the SkiaSharp canvas</li>
<li>Automatic snapshot updates when control content changes</li>
<li>Optimized for performance with caching</li>
</ul>
<p><strong>iOS/Android:</strong></p>
<ul>
<li>Direct native view positioning and transformation</li>
<li>No snapshot overhead - native controls are moved/transformed directly</li>
<li>Better performance and native feel</li>
</ul>
<h3 id="common-integration-scenarios">Common Integration Scenarios</h3>
<h4 id="media-playback">Media Playback</h4>
<pre><code class="lang-xml">&lt;draw:SkiaMauiElement
    HorizontalOptions=&quot;Fill&quot;
    HeightRequest=&quot;200&quot;&gt;
    
    &lt;MediaElement
        Source=&quot;video.mp4&quot;
        ShowsPlaybackControls=&quot;True&quot;
        AutoPlay=&quot;False&quot; /&gt;
        
&lt;/draw:SkiaMauiElement&gt;
</code></pre>
<h4 id="datetime-pickers">Date/Time Pickers</h4>
<pre><code class="lang-xml">&lt;draw:SkiaLayout Type=&quot;Column&quot; Spacing=&quot;16&quot;&gt;
    
    &lt;draw:SkiaMauiElement HeightRequest=&quot;50&quot;&gt;
        &lt;DatePicker
            Date=&quot;{Binding SelectedDate}&quot;
            Format=&quot;dd/MM/yyyy&quot; /&gt;
    &lt;/draw:SkiaMauiElement&gt;
    
    &lt;draw:SkiaMauiElement HeightRequest=&quot;50&quot;&gt;
        &lt;TimePicker
            Time=&quot;{Binding SelectedTime}&quot;
            Format=&quot;HH:mm&quot; /&gt;
    &lt;/draw:SkiaMauiElement&gt;
    
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h4 id="native-picker">Native Picker</h4>
<pre><code class="lang-xml">&lt;draw:SkiaMauiElement HeightRequest=&quot;50&quot;&gt;
    &lt;Picker
        Title=&quot;Select an option&quot;
        ItemsSource=&quot;{Binding Options}&quot;
        SelectedItem=&quot;{Binding SelectedOption}&quot; /&gt;
&lt;/draw:SkiaMauiElement&gt;
</code></pre>
<h3 id="properties">Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>Content</code></td>
<td>VisualElement</td>
<td>The native MAUI control to embed</td>
</tr>
</tbody>
</table>
<h3 id="important-notes">Important Notes</h3>
<ul>
<li><strong>Content Property</strong>: Use the <code>Content</code> property to set the embedded control, not child elements</li>
<li><strong>Sizing</strong>: The SkiaMauiElement will size itself based on the embedded control's requirements</li>
<li><strong>Gestures</strong>: Native controls handle their own gestures; DrawnUI gestures work outside the embedded area</li>
<li><strong>Performance</strong>: Consider the platform-specific rendering approach when designing your layout</li>
<li><strong>Binding Context</strong>: The embedded control automatically inherits the binding context</li>
</ul>
<h3 id="limitations">Limitations</h3>
<ul>
<li>Cannot have SkiaControl subviews (use Content property instead)</li>
<li>Platform-specific rendering differences may affect visual consistency</li>
<li>Some complex native controls may have gesture conflicts</li>
</ul>
<h3 id="best-practices">Best Practices</h3>
<ol>
<li><strong>Use Sparingly</strong>: Only embed native controls when necessary (e.g., WebView, MediaElement)</li>
<li><strong>Size Appropriately</strong>: Set explicit sizes when possible to avoid layout issues</li>
<li><strong>Test on All Platforms</strong>: Verify behavior across iOS, Android, and Windows</li>
<li><strong>Consider Alternatives</strong>: Check if DrawnUI has a native equivalent before embedding</li>
<li><strong>Performance</strong>: Monitor performance impact, especially with multiple embedded controls</li>
</ol>
<h2 id="skiacamera">SkiaCamera</h2>
<p>SkiaCamera is a specialized control that provides camera functionality directly within the DrawnUI canvas. It allows you to capture photos and video while maintaining the performance and visual consistency of the DrawnUI rendering pipeline.</p>
<h3 id="basic-usage-1">Basic Usage</h3>
<pre><code class="lang-xml">&lt;draw:SkiaCamera
    x:Name=&quot;CameraControl&quot;
    IsOn=&quot;True&quot;
    Facing=&quot;Default&quot;
    CameraIndex=&quot;-1&quot;
    FlashMode=&quot;Off&quot;
    CaptureFlashMode=&quot;Auto&quot;
    CapturePhotoQuality=&quot;Medium&quot;
    CaptureFormatIndex=&quot;0&quot;
    WidthRequest=&quot;300&quot;
    HeightRequest=&quot;400&quot; /&gt;
</code></pre>
<h3 id="key-properties">Key Properties</h3>
<table>
<thead>
<tr>
<th>Property</th>
<th>Type</th>
<th>Default</th>
<th>Description</th>
</tr>
</thead>
<tbody>
<tr>
<td><code>IsOn</code></td>
<td>bool</td>
<td>false</td>
<td>Camera power state - use this to start/stop camera</td>
</tr>
<tr>
<td><code>Facing</code></td>
<td>CameraPosition</td>
<td>Default</td>
<td>Camera selection: Default (back), Selfie (front), Manual</td>
</tr>
<tr>
<td><code>CameraIndex</code></td>
<td>int</td>
<td>-1</td>
<td>Manual camera selection index (when Facing = Manual)</td>
</tr>
<tr>
<td><code>State</code></td>
<td>CameraState</td>
<td>-</td>
<td>Current camera state (read-only)</td>
</tr>
<tr>
<td><code>IsBusy</code></td>
<td>bool</td>
<td>-</td>
<td>Processing state (read-only)</td>
</tr>
<tr>
<td><code>FlashMode</code></td>
<td>FlashMode</td>
<td>Off</td>
<td>Preview torch mode: Off, On, Strobe</td>
</tr>
<tr>
<td><code>CaptureFlashMode</code></td>
<td>CaptureFlashMode</td>
<td>Auto</td>
<td>Flash mode for capture: Off, Auto, On</td>
</tr>
<tr>
<td><code>CapturePhotoQuality</code></td>
<td>CaptureQuality</td>
<td>Max</td>
<td>Photo quality: Max, Medium, Low, Preview, Manual</td>
</tr>
<tr>
<td><code>CaptureFormatIndex</code></td>
<td>int</td>
<td>0</td>
<td>Format index for manual capture (when CapturePhotoQuality = Manual)</td>
</tr>
<tr>
<td><code>CurrentStillCaptureFormat</code></td>
<td>CaptureFormat</td>
<td>-</td>
<td>Currently selected capture format (read-only)</td>
</tr>
<tr>
<td><code>IsFlashSupported</code></td>
<td>bool</td>
<td>-</td>
<td>Whether flash is available (read-only)</td>
</tr>
<tr>
<td><code>IsAutoFlashSupported</code></td>
<td>bool</td>
<td>-</td>
<td>Whether auto flash is supported (read-only)</td>
</tr>
<tr>
<td><code>Zoom</code></td>
<td>double</td>
<td>1.0</td>
<td>Camera zoom level</td>
</tr>
<tr>
<td><code>ZoomLimitMin</code></td>
<td>double</td>
<td>1.0</td>
<td>Minimum zoom level</td>
</tr>
<tr>
<td><code>ZoomLimitMax</code></td>
<td>double</td>
<td>10.0</td>
<td>Maximum zoom level</td>
</tr>
<tr>
<td><code>Effect</code></td>
<td>SkiaImageEffect</td>
<td>-</td>
<td>Real-time color filters for preview</td>
</tr>
</tbody>
</table>
<h3 id="examples">Examples</h3>
<pre><code class="lang-xml">&lt;!-- Basic camera with controls --&gt;
&lt;draw:SkiaLayout Type=&quot;Column&quot; Spacing=&quot;10&quot;&gt;
    &lt;draw:SkiaCamera
        x:Name=&quot;Camera&quot;
        IsOn=&quot;True&quot;
        Facing=&quot;Default&quot;
        FlashMode=&quot;Off&quot;
        CaptureFlashMode=&quot;Auto&quot;
        CapturePhotoQuality=&quot;Medium&quot;
        WidthRequest=&quot;300&quot;
        HeightRequest=&quot;400&quot; /&gt;

    &lt;draw:SkiaLayout Type=&quot;Row&quot; Spacing=&quot;10&quot;&gt;
        &lt;draw:SkiaButton
            Text=&quot;Capture&quot;
            Clicked=&quot;OnCaptureClicked&quot; /&gt;
        &lt;draw:SkiaButton
            Text=&quot;Toggle Torch&quot;
            Clicked=&quot;OnToggleTorchClicked&quot; /&gt;
        &lt;draw:SkiaButton
            Text=&quot;Switch Camera&quot;
            Clicked=&quot;OnSwitchCameraClicked&quot; /&gt;
    &lt;/draw:SkiaLayout&gt;
&lt;/draw:SkiaLayout&gt;
</code></pre>
<h3 id="code-behind-example">Code-Behind Example</h3>
<pre><code class="lang-csharp">private async void OnCaptureClicked(object sender, EventArgs e)
{
    try
    {
        await Camera.TakePicture();
        // Photo will be delivered via CaptureSuccess event
    }
    catch (Exception ex)
    {
        // Handle error
        await DisplayAlert(&quot;Error&quot;, $&quot;Failed to capture photo: {ex.Message}&quot;, &quot;OK&quot;);
    }
}

private void OnCaptureSuccess(object sender, CapturedImage captured)
{
    // Handle captured photo
    MainThread.BeginInvokeOnMainThread(async () =&gt;
    {
        await SavePhotoAsync(captured);
    });
}

private void OnToggleTorchClicked(object sender, EventArgs e)
{
    // Toggle preview torch using property-based approach
    Camera.FlashMode = Camera.FlashMode == FlashMode.Off
        ? FlashMode.On
        : FlashMode.Off;
}

private void OnSwitchCameraClicked(object sender, EventArgs e)
{
    Camera.Facing = Camera.Facing == CameraPosition.Default
        ? CameraPosition.Selfie
        : CameraPosition.Default;
}
</code></pre>
<h3 id="flash-control">Flash Control</h3>
<p>SkiaCamera provides comprehensive flash control with independent preview torch and capture flash modes:</p>
<pre><code class="lang-csharp">// Preview torch control
Camera.FlashMode = FlashMode.Off;    // Disable torch
Camera.FlashMode = FlashMode.On;     // Enable torch
Camera.FlashMode = FlashMode.Strobe; // Strobe mode (future feature)

// Capture flash control
Camera.CaptureFlashMode = CaptureFlashMode.Off;   // No flash
Camera.CaptureFlashMode = CaptureFlashMode.Auto;  // Auto flash
Camera.CaptureFlashMode = CaptureFlashMode.On;    // Always flash

// Check flash capabilities
if (Camera.IsFlashSupported)
{
    // Flash is available on this camera
    Camera.FlashMode = FlashMode.On;
}
</code></pre>
<p><strong>Key Features:</strong></p>
<ul>
<li><strong>Independent Control</strong>: Preview torch and capture flash work separately</li>
<li><strong>Property-Based API</strong>: Modern, bindable properties for MVVM scenarios</li>
<li><strong>Clean API</strong>: Simple property-based approach without legacy methods</li>
<li><strong>Future Extensibility</strong>: Ready for strobe and other advanced flash modes</li>
</ul>
<h3 id="camera-management">Camera Management</h3>
<p>SkiaCamera provides comprehensive camera enumeration and selection capabilities:</p>
<pre><code class="lang-csharp">// Get available cameras
var cameras = await Camera.GetAvailableCamerasAsync();
foreach (var camera in cameras)
{
    Debug.WriteLine($&quot;Camera {camera.Index}: {camera.Name} ({camera.Position})&quot;);
    Debug.WriteLine($&quot;  ID: {camera.Id}&quot;);
    Debug.WriteLine($&quot;  Has Flash: {camera.HasFlash}&quot;);
}

// Manual camera selection
Camera.Facing = CameraPosition.Manual;
Camera.CameraIndex = 2; // Select third camera
Camera.IsOn = true;

// Automatic selection
Camera.Facing = CameraPosition.Default; // Back camera
Camera.Facing = CameraPosition.Selfie;  // Front camera
</code></pre>
<h3 id="capture-format-management">Capture Format Management</h3>
<p>Control capture resolution and quality with flexible format selection:</p>
<pre><code class="lang-csharp">// Quality presets
Camera.CapturePhotoQuality = CaptureQuality.Max;     // Highest resolution
Camera.CapturePhotoQuality = CaptureQuality.Medium;  // Balanced quality/size
Camera.CapturePhotoQuality = CaptureQuality.Low;     // Fastest capture
Camera.CapturePhotoQuality = CaptureQuality.Preview; // Smallest usable size

// Manual format selection
var formats = await Camera.GetAvailableCaptureFormatsAsync();
Camera.CapturePhotoQuality = CaptureQuality.Manual;
Camera.CaptureFormatIndex = 0; // Select first format

// Read current format
var currentFormat = Camera.CurrentStillCaptureFormat;
if (currentFormat != null)
{
    Debug.WriteLine($&quot;Current: {currentFormat.Width}x{currentFormat.Height}&quot;);
    Debug.WriteLine($&quot;Aspect: {currentFormat.AspectRatioString}&quot;);
    Debug.WriteLine($&quot;Pixels: {currentFormat.TotalPixels:N0}&quot;);
}
</code></pre>
<h3 id="core-methods">Core Methods</h3>
<pre><code class="lang-csharp">// Camera Management
public async Task&lt;List&lt;CameraInfo&gt;&gt; GetAvailableCamerasAsync()
public async Task&lt;List&lt;CameraInfo&gt;&gt; RefreshAvailableCamerasAsync()
public static void CheckPermissions(Action&lt;bool&gt; callback)

// Capture Format Management
public async Task&lt;List&lt;CaptureFormat&gt;&gt; GetAvailableCaptureFormatsAsync()
public async Task&lt;List&lt;CaptureFormat&gt;&gt; RefreshAvailableCaptureFormatsAsync()
public CaptureFormat CurrentStillCaptureFormat { get; }

// Capture Operations
public async Task TakePicture()
public void FlashScreen(Color color, long duration = 250)
public void OpenFileInGallery(string filePath)

// Camera Controls
public void SetZoom(double value)

// Flash Control Methods
public void SetFlashMode(FlashMode mode)
public FlashMode GetFlashMode()
public void SetCaptureFlashMode(CaptureFlashMode mode)
public CaptureFlashMode GetCaptureFlashMode()
</code></pre>
<h3 id="events">Events</h3>
<pre><code class="lang-csharp">public event EventHandler&lt;CapturedImage&gt; CaptureSuccess;
public event EventHandler&lt;Exception&gt; CaptureFailed;
public event EventHandler&lt;LoadedImageSource&gt; NewPreviewSet;
public event EventHandler&lt;CameraState&gt; StateChanged;
public event EventHandler&lt;string&gt; OnError;
public event EventHandler&lt;double&gt; Zoomed;
</code></pre>
<h3 id="data-classes">Data Classes</h3>
<pre><code class="lang-csharp">// Capture format information
public class CaptureFormat
{
    public int Width { get; set; }                    // Width in pixels
    public int Height { get; set; }                   // Height in pixels
    public int TotalPixels =&gt; Width * Height;         // Total pixel count
    public double AspectRatio =&gt; (double)Width / Height; // Decimal aspect ratio
    public string AspectRatioString { get; }          // Standard notation (&quot;16:9&quot;, &quot;4:3&quot;)
    public string FormatId { get; set; }              // Platform-specific identifier
    public string Description { get; }               // Human-readable description
}

// Camera information
public class CameraInfo
{
    public string Id { get; set; }                    // Platform camera ID
    public string Name { get; set; }                  // Display name
    public CameraPosition Position { get; set; }      // Camera position
    public int Index { get; set; }                    // Camera index
    public bool HasFlash { get; set; }                // Flash availability
}
</code></pre>
<h3 id="enums">Enums</h3>
<pre><code class="lang-csharp">public enum CameraPosition { Default, Selfie, Manual }
public enum CameraState { Off, On, Error }
public enum CaptureQuality { Max, Medium, Low, Preview, Manual }
public enum FlashMode { Off, On, Strobe }
public enum CaptureFlashMode { Off, Auto, On }
public enum SkiaImageEffect { None, Sepia, BlackAndWhite, Pastel }
</code></pre>
<h3 id="gallery-integration">Gallery Integration</h3>
<p>SkiaCamera provides <code>OpenFileInGallery()</code> method to open captured photos in the system gallery:</p>
<pre><code class="lang-csharp">private async void OnCaptureClicked(object sender, EventArgs e)
{
    try
    {
        await Camera.TakePicture();
        // Photo will be delivered via CaptureSuccess event
    }
    catch (Exception ex)
    {
        await DisplayAlert(&quot;Error&quot;, $&quot;Failed to capture photo: {ex.Message}&quot;, &quot;OK&quot;);
    }
}

private async void OnCaptureSuccess(object sender, CapturedImage captured)
{
    try
    {
        // Save photo to file
        var fileName = $&quot;photo_{DateTime.Now:yyyyMMdd_HHmmss}.jpg&quot;;
        var filePath = Path.Combine(FileSystem.Current.CacheDirectory, fileName);

        using var fileStream = File.Create(filePath);
        using var data = captured.Image.Encode(SKEncodedImageFormat.Jpeg, 90);
        data.SaveTo(fileStream);

        // Open in system gallery
        Camera.OpenFileInGallery(filePath);
    }
    catch (Exception ex)
    {
        await DisplayAlert(&quot;Error&quot;, $&quot;Failed to open in gallery: {ex.Message}&quot;, &quot;OK&quot;);
    }
}
</code></pre>
<p><strong>Android FileProvider Setup Required:</strong></p>
<p>For Android, you must configure a FileProvider in <code>AndroidManifest.xml</code>:</p>
<pre><code class="lang-xml">&lt;application&gt;
    &lt;provider
        android:name=&quot;androidx.core.content.FileProvider&quot;
        android:authorities=&quot;${applicationId}.fileprovider&quot;
        android:exported=&quot;false&quot;
        android:grantUriPermissions=&quot;true&quot;&gt;
        &lt;meta-data
            android:name=&quot;android.support.FILE_PROVIDER_PATHS&quot;
            android:resource=&quot;@xml/file_paths&quot; /&gt;
    &lt;/provider&gt;
&lt;/application&gt;
</code></pre>
<p>Create <code>Platforms/Android/Resources/xml/file_paths.xml</code>:</p>
<pre><code class="lang-xml">&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-8&quot;?&gt;
&lt;paths xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;&gt;
    &lt;external-files-path name=&quot;my_images&quot; path=&quot;Pictures&quot; /&gt;
    &lt;cache-path name=&quot;my_cache&quot; path=&quot;.&quot; /&gt;
&lt;/paths&gt;
</code></pre>

</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/drawnui/blob/main/docs/articles/controls/native-integration.md/#L1" class="edit-link">Edit this page</a>
        </div>

        <div class="next-article d-print-none border-top" id="nextArticle"></div>

      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          Made by <a href="https://taublast.github.io/about/">Nick Kovalsky aka AppoMobi (@taublast)</a>
        </div>
      </div>
    </footer>
  </body>
</html>
