# Sample Apps

Explore real-world applications and demo projects built with DrawnUI to see the framework in action.

## 🎮 Learning Projects

- **[Engine Demo](https://github.com/taublast/AppoMobi.Maui.DrawnUi.Demo)** 🤩 - A comprehensive totally drawn app demo featuring:
  - Navigation on the canvas with SkiaShell
  - Recycled cells and virtualization
  - Camera integration examples
  - Custom controls showcase

- **[Sandbox Project](https://github.com/taublast/DrawnUi.Maui/tree/main/src/Maui/Samples/Sandbox)** 🧪 - Experiment with:
  - Playground examples
  - Custom controls development
  - Maps integration
  - Various styling approaches

- **[Shaders Carousel](https://github.com/taublast/ShadersCarousel/)** ✨ - Advanced SkiaSharp capabilities
  - Custom shader effects
  - Visual effects showcase
  - Hardware acceleration examples
  - Modern rendering techniques

## ⭐️ Published Apps

Real-world applications built with DrawnUI:

### Bricks Breaker
- **Browse Code**: [Github repository](https://github.com/taublast/DrawnUi.Breakout)
- **Install**: [AppStore](https://apps.apple.com/us/app/bricks-breaker/id6749823869?platform=iphone), [Google Play](https://play.google.com/store/apps/details?id=com.appomobi.drawnui.breakout)
- **Architecture**: Single Canvas root with drawn dialogs
- **Features**: Breakout/Arkanoid style arcade game 


### Bug ID: Insect Identifier AI
_Totally drawn with just one root view `Canvas` and `SkiaShell` for navigation. First ever totally drawn published MAUI app._

- **Video Demo**: [Watch on YouTube](https://www.youtube.com/watch?v=5QIaM0xsLbA)
- **Architecture**: Single Canvas root with SkiaShell navigation
- **Features**: AI-powered insect identification with fully drawn UI

### Racebox - Vehicle Dynamics
_MAUI native pages with canvases and custom navigation. All scrolls, cells collections, maps, buttons, labels and custom controls are drawn._
- **Install**: [AppStore](https://apps.apple.com/us/app/racebox-%D0%B4%D0%B8%D0%BD%D0%B0%D0%BC%D0%B8%D0%BA%D0%B0-%D0%B0%D0%B2%D1%82%D0%BE/id6444165250?platform=iphone), [Google Play](https://play.google.com/store/apps/details?id=com.raceboxcompanion.app)
- **Video Demo**: [Watch on YouTube](https://www.youtube.com/watch?v=JQkJhXR9IMY)
- **Architecture**: MAUI pages with Canvas components
- **Features**: Vehicle telemetry, maps, data visualization, custom controls

### Art Of Foto - Photo Tools
_MAUI native pages with canvases and custom navigation.._
- **Install**: [AppStore](https://apps.apple.com/ru/app/art-of-foto/id1440728210?platform=iphone), [Google Play](https://play.google.com/store/apps/details?id=com.appomobi.artoffoto)
- **Architecture**: MAUI pages with Canvas components
- **Features**: Vehicle telemetry, maps, data visualization, custom controls


## Contributing Samples
Have a cool UI or feature? Submit a PR or open an issue to share your sample with the community!
